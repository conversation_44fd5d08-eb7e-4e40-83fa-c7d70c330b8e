"use client";

import React from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { getStore, getPersistor } from "./index";

// ============================================================================
// STORE PROVIDER COMPONENT
// ============================================================================

interface StoreProviderProps {
  children: React.ReactNode;
}

/**
 * Redux Store Provider with persistence support
 */
export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const store = getStore();
  const persistor = getPersistor();

  return (
    <Provider store={store}>
      <PersistGate loading={<StoreLoadingFallback />} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
};

/**
 * Loading fallback component shown during store rehydration
 */
const StoreLoadingFallback: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600 text-sm">Loading your preferences...</p>
      </div>
    </div>
  );
};

export default StoreProvider;
