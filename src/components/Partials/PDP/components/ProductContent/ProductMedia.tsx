import React, { useState, useMemo } from "react";
import ProductCarouselSection from "../../ProductCarouselSection";
import ProductRatingSection from "../../ProductRatingSection";
import { ProductMediaProps } from "../../types";

/**
 * ProductMedia Component
 *
 * Contains product carousel and rating section
 */
export const ProductMedia: React.FC<ProductMediaProps> = ({
  strapiProduct,
  medusaProduct,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Generate combined images using the same logic as the useProductImages hook
  const images = useMemo(() => {
    const globalImages: string[] = [];
    const variantImages: string[] = [];
    const activeVariant = medusaProduct?.variants?.[0]; // Use first variant

    // Extract global product images
    if (medusaProduct?.images && Array.isArray(medusaProduct.images)) {
      globalImages.push(
        ...medusaProduct.images.map((img: any) => img.url || img)
      );
    }

    // Extract variant-specific images
    if (
      activeVariant?.variant_image &&
      Array.isArray(activeVariant.variant_image)
    ) {
      const sortedVariantImages = [...activeVariant.variant_image].sort(
        (a, b) => a.rank - b.rank
      );
      variantImages.push(...sortedVariantImages.map((img) => img.url));
    }

    // Combine images
    const combined = [...globalImages, ...variantImages];

    // Fallback to sample images if no images are available
    if (combined.length === 0) {
      return [
        "/images/products/pdp/image.png",
        "/images/products/badaam/highlight/2.webp",
        "/images/products/badaam/highlight/3.webp",
        "/images/products/badaam/highlight/4.webp",
        "/images/products/badaam/highlight/5.webp",
      ];
    }

    return combined;
  }, [medusaProduct?.images, medusaProduct?.variants]);

  return (
    <>
      {/* Product Carousel */}
      <ProductCarouselSection
        images={images}
        title={medusaProduct?.title || "Product"}
        currentSlide={currentSlide}
        onSlideChange={setCurrentSlide}
        primaryColor={primaryColor}
      />

      {/* Rating and Reviews */}
      <ProductRatingSection
        rating={4.0}
        reviewCount={306}
        reviewsHref="#reviews"
        primaryColor={primaryColor}
      />
    </>
  );
};
