import React, { useState } from "react";
import Minus from "@/assets/icons/Minus";
import Plus from "@/assets/icons/Plus";
import { QuantitySelectorProps } from "../types";

/**
 * QuantitySelector Component
 *
 * Displays quantity selection controls with increment/decrement buttons.
 * Uses local state for quantity management and props for theming.
 */
const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  label = "BOX",
}) => {
  const [quantity, setQuantity] = useState(1);
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  const handleDecrease = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleIncrease = () => {
    setQuantity(quantity + 1);
  };

  return (
    <div className="mb-4">
      <div
        className="h-10 rounded-sm overflow-hidden flex items-center justify-between border bg-white"
        style={{ borderColor: primaryColor }}
      >
        <button
          className="h-10 w-10 flex items-center justify-center"
          style={{ backgroundColor: primaryColor }}
          onClick={handleDecrease}
        >
          <Minus />
        </button>
        <label
          className="flex-1 w-full text-center uppercase text-sm font-[550] leading-5 font-obviously"
          style={{ color: primaryColor }}
        >
          {quantity} {label}
        </label>
        <button
          className="h-10 w-10 flex items-center justify-center"
          style={{ backgroundColor: primaryColor }}
          onClick={handleIncrease}
        >
          <Plus />
        </button>
      </div>
    </div>
  );
};

export default QuantitySelector;
