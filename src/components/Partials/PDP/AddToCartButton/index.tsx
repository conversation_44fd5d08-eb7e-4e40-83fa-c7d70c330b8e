import React, { useState, useMemo } from "react";
import { AddToCartButtonProps } from "../types";

/**
 * AddToCartButton Component
 *
 * Displays the add to cart button with loading state support.
 * Uses props for data and theming instead of Redux.
 *
 * @param onAddToCart - Optional external callback function when button is clicked
 */
const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  strapiProduct,
  medusaProduct,
  onAddToCart,
}) => {
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Calculate button state based on available variants
  const buttonState = useMemo(() => {
    const variants = medusaProduct?.variants || [];
    const hasVariants = variants.length > 0;
    const activeVariant = variants[0]; // Use first variant as default

    if (isAddingToCart) {
      return {
        text: "Adding...",
        disabled: true,
      };
    }

    if (!hasVariants || !activeVariant) {
      return {
        text: "Select Variant",
        disabled: true,
      };
    }

    return {
      text: "Add to Cart",
      disabled: false,
    };
  }, [medusaProduct?.variants, isAddingToCart]);

  const handleAddToCart = async () => {
    if (buttonState.disabled) return;

    setIsAddingToCart(true);

    try {
      // Call external callback if provided
      if (onAddToCart && medusaProduct?.variants?.[0]) {
        const activeVariant = medusaProduct.variants[0];
        const currentPrice =
          activeVariant.extended_product_variants?.discounted_price ||
          activeVariant.calculated_price?.calculated_amount ||
          0;

        onAddToCart({
          product: medusaProduct,
          variant: activeVariant,
          quantity: 1,
          totalPrice: currentPrice,
        });
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setIsAddingToCart(false);
    }
  };
  return (
    <div className="mb-4">
      <button
        className="h-12.5 uppercase w-full p-2 rounded-[6px] text-white text-center font-semibold text-sm font-obviously border"
        style={{
          backgroundColor: primaryColor,
          borderColor: primaryColor,
        }}
        onClick={handleAddToCart}
        disabled={buttonState.disabled}
      >
        {buttonState.text}
      </button>
    </div>
  );
};

export default AddToCartButton;
