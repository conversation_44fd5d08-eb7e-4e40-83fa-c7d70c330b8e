import { useMemo } from "react";
import {
  useProductUI as useReduxProductUI,
  useProductTheme as useReduxProductTheme,
  useProductData as useReduxProductData,
} from "@/store/hooks";

/**
 * Hook for managing product UI state
 * Provides carousel, modal, and other UI-related functionality
 */
export const useProductUI = () => {
  const {
    currentSlide,
    setCurrentSlide,
    isModalOpen,
    selectedImageIndex,
    openModal,
    closeModal,
    images: combinedImages,
  } = useReduxProductUI();

  return useMemo(
    () => ({
      // Carousel state
      currentSlide,
      setCurrentSlide,

      // Modal state
      isModalOpen,
      selectedImageIndex,
      openModal,
      closeModal,

      // Images
      images: combinedImages,
      hasImages: combinedImages.length > 0,
      imageCount: combinedImages.length,
    }),
    [
      currentSlide,
      setCurrentSlide,
      isModalOpen,
      selectedImageIndex,
      openModal,
      closeModal,
      combinedImages,
    ]
  );
};

/**
 * Hook for carousel navigation
 */
export const useCarouselNavigation = () => {
  const { currentSlide, setCurrentSlide, combinedImages } = useReduxProductUI();

  const canGoNext = useMemo(() => {
    return currentSlide < combinedImages.length - 1;
  }, [currentSlide, combinedImages.length]);

  const canGoPrevious = useMemo(() => {
    return currentSlide > 0;
  }, [currentSlide]);

  const goToNext = useMemo(
    () => () => {
      if (canGoNext) {
        setCurrentSlide(currentSlide + 1);
      }
    },
    [canGoNext, currentSlide, setCurrentSlide]
  );

  const goToPrevious = useMemo(
    () => () => {
      if (canGoPrevious) {
        setCurrentSlide(currentSlide - 1);
      }
    },
    [canGoPrevious, currentSlide, setCurrentSlide]
  );

  const goToSlide = useMemo(
    () => (index: number) => {
      if (index >= 0 && index < combinedImages.length) {
        setCurrentSlide(index);
      }
    },
    [combinedImages.length, setCurrentSlide]
  );

  return {
    currentSlide,
    canGoNext,
    canGoPrevious,
    goToNext,
    goToPrevious,
    goToSlide,
    totalSlides: combinedImages.length,
  };
};

/**
 * Hook for modal management
 */
export const useProductModal = () => {
  const {
    isModalOpen,
    selectedImageIndex,
    openModal,
    closeModal,
    images: combinedImages,
  } = useReduxProductUI();

  const openImageModal = useMemo(
    () => (imageIndex?: number) => {
      openModal(imageIndex);
    },
    [openModal]
  );

  const currentModalImage = useMemo(() => {
    return combinedImages[selectedImageIndex] || combinedImages[0] || "";
  }, [combinedImages, selectedImageIndex]);

  return {
    isModalOpen,
    selectedImageIndex,
    openModal: openImageModal,
    closeModal,
    currentModalImage,
    hasModalImage: !!currentModalImage,
  };
};

/**
 * Hook for theme and styling
 */
export const useProductTheme = () => {
  const { primaryColor, backgroundColor } = useReduxProductTheme();

  const themeStyles = useMemo(
    () => ({
      primaryColor,
      backgroundColor,
      cssVariables: {
        "--product-primary-color": primaryColor,
        "--product-background-color": backgroundColor,
      } as React.CSSProperties,
    }),
    [primaryColor, backgroundColor]
  );

  return {
    primaryColor,
    backgroundColor,
    ...themeStyles,
  };
};

/**
 * Hook for responsive behavior
 */
export const useProductResponsive = () => {
  // This could be enhanced with actual breakpoint detection
  // For now, we'll provide the structure

  return useMemo(
    () => ({
      isMobile: false, // Could use a breakpoint hook here
      isTablet: false,
      isDesktop: true,
      showMobileCarousel: false, // Based on screen size
      showDesktopCarousel: true,
    }),
    []
  );
};

/**
 * Hook for loading and error states
 */
export const useProductStatus = () => {
  const { isLoading, isError, error } = useReduxProductData();

  return useMemo(
    () => ({
      isLoading,
      isError,
      error,
      isReady: !isLoading && !isError,
      hasError: isError && !!error,
    }),
    [isLoading, isError, error]
  );
};
