"use client";
import React from "react";
import { ProductLayout } from "./components/ProductLayout";
import { ProductInfo } from "./components/ProductInfo";
import { ProductContent } from "./components/ProductContent";
import { ProductDetailsPageProps } from "./types";

/**
 * ProductDetailsPage Component
 *
 * Main product details page component with server-side data:
 * - Receives all data as props from server-side fetching
 * - Eliminates prop drilling by passing data to child components
 * - Provides clean component structure
 * - Maintains identical UI and functionality
 * - Loads instantly without loading states
 */
export const ProductDetailsPage: React.FC<ProductDetailsPageProps> = ({
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
}) => {
  // Apply theme styles using CSS custom properties
  const themeStyles = {
    "--product-primary-color": strapiProduct?.primary_color || "#036A38",
    "--product-background-color": strapiProduct?.bg_color || "#ffffff",
  } as React.CSSProperties;

  return (
    <div style={themeStyles}>
      <ProductLayout>
        {/* Product Information Section */}
        <ProductInfo
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
          onAddToCart={onAddToCart}
          onCouponClick={onCouponClick}
        />

        {/* Product Content Section */}
        <ProductContent
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
        />
      </ProductLayout>
    </div>
  );
};

// Export as default for backward compatibility
export default ProductDetailsPage;
